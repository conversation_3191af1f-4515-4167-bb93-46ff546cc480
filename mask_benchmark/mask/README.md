# Model Evaluation Framework

This framework accompanies our research paper "Beyond Accuracy: Disentangling Honesty and Factuality in AI Models" which introduces MASK (Model Alignment between Statements and Knowledge), a novel benchmark for measuring honesty in large language models. Our research demonstrates that while larger models obtain higher accuracy, they do not necessarily become more honest when pressured to lie. We introduce a consistency-based honesty metric that disentangles accuracy from honesty by testing whether models will contradict their own beliefs under pressure. The framework provides tools for evaluating language models' responses across different scenarios, focusing on this critical distinction between knowing facts (accuracy) and choosing to state them truthfully (honesty).

## Overview

The framework consists of three main stages:

1. Response Generation
2. Response Evaluation
3. Metrics Processing

## Setup

1. Create a `.env` file in the root directory with your API keys:
```env
OPENAI_API_KEY=your_key_here # needed for evaluation
ANTHROPIC_API_KEY=your_key_here
```

2. Install dependencies (recommended to use a virtual environment):
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

For details on specific dependencies, see the "Dependencies" section below.

3. Download data:
```bash
python download.py
```


## Dependencies

The framework, including enhanced multi-GPU support, requires several key packages. These can be installed by running `pip install -r requirements.txt`.
Key dependencies include:

- `torch >= 1.9.0`
- `transformers >= 4.20.0`
- `accelerate >= 0.20.0`
- `pynvml` (for GPU monitoring)


## Directory Structure

```
├── csv_data/               # Production data directory
│   ├── responses/         # Generated model responses
│   ├── evaluated/         # Evaluated responses
│   └── metrics/          # Processed metrics
├── test_csv_data/         # Test data directory (same structure as csv_data)
├── prompts/               # Evaluation prompt templates
└── *.py                   # Main Python scripts
```


## Usage

### 1. Generate Responses

Generate responses using the `generate_responses.py` script.
Basic command:

```bash
python generate_responses.py [--test] [--model MODEL_NAME] [--temperature TEMP] [--lie_k NUM_RUNS]
```

#### High-Performance vLLM Backend (Recommended)

For 10x faster inference with GPU optimization and progress tracking:

```bash
# Single GPU on specific hardware
CUDA_VISIBLE_DEVICES=4,5,6,7 python generate_responses.py \
    --model Qwen/Qwen3-8B \
    --thinking \
    --use_vllm \
    --vllm_gpus 1 \
    --gpu_ids "4,5,6,7"

# Multi-GPU data parallelism
CUDA_VISIBLE_DEVICES=4,5,6,7 python generate_responses.py \
    --model Qwen/Qwen3-8B \
    --thinking \
    --use_vllm \
    --vllm_gpus 4 \
    --gpu_ids "4,5,6,7"
```

**vLLM Features:**
- ✅ **10x Faster Inference**: Optimized CUDA kernels and memory management
- ✅ **Precise GPU Selection**: Use specific GPUs (e.g., 4,5,6,7) instead of 0,1,2,3
- ✅ **Data Parallelism**: Split data across multiple GPUs for parallel processing
- ✅ **Progress Bars**: Visual feedback for data processing and GPU workers
- ✅ **Memory Optimization**: Automatic memory management and optimization

**General Options:**

- `--test`: Use test directories.
- `--model`: Model name to use (default: gpt-4o).
- `--temperature`: Sampling temperature (default: 1.0).
- `--lie_k`: Number of lying prompts to generate per row (default: 1).

The script supports OpenAI and Anthropic models. Generated responses will be saved in `csv_data/responses/` following the naming convention: `{archetype}_{model}.csv`.

**Multi-GPU Support for `generate_responses.py`**

The `generate_responses.py` script includes enhanced multi-GPU capabilities for efficient parallel inference with both Hugging Face transformers and vLLM backends.

**Overview of Multi-GPU Approaches**

* **🚀 vLLM Backend (Recommended)**
    * **Data Parallelism**: Splits input data across multiple GPUs, each running a separate vLLM instance.
    * **High Performance**: Optimized inference engine with PagedAttention and continuous batching.
    * **True Parallelism**: Each GPU processes different data subsets simultaneously.
    * **Automatic Scaling**: Efficiently utilizes all available GPUs.
* **🔧 Transformers Backend (Legacy)**

1. **Automatic Strategy Selection**: Intelligently chooses the best approach based on available hardware.
2. **Parallel Instances**: Multiple model copies across GPUs for maximum throughput.
3. **Pipeline Parallelism**: Single large model distributed across GPUs.
4. **Single GPU**: Traditional single-GPU approach with optimizations.

**New Features for Multi-GPU Support**

* **Enhanced GPU Management**: Automatic GPU detection and selection, memory-aware GPU allocation, dynamic load balancing, and comprehensive GPU memory monitoring.
* **Multiple Inference Strategies**:
    * `auto`: Automatically selects the best strategy.
    * `parallel`: Separate model instances on each GPU.
    * `pipeline`: Distributes a single large model across GPUs.
    * `single`: Uses one GPU with optimizations.
* **Advanced Configuration Options**: GPU selection by ID, minimum memory requirements, configurable batch sizes, and memory optimization controls.

**Multi-GPU Usage Examples**

**vLLM Backend (Recommended)**
Basic vLLM Usage:

```bash
python generate_responses.py \
    --model meta-llama/Llama-3.1-8B-Instruct \
    --use_vllm \
    --vllm_gpus 4
```

vLLM with Specific GPUs:

```bash
python generate_responses.py \
    --model meta-llama/Llama-3.1-8B-Instruct \
    --use_vllm \
    --vllm_gpus 2 \
    --gpu_ids "0,1"
```

**Transformers Backend (Legacy)**

```bash
python generate_responses.py \
    --model meta-llama/Llama-3.1-8B-Instruct \
    --multi_gpu_strategy parallel \
    --num_gpus 4 \
    --min_memory_gb 8.0 \
    --gpu_ids "0,1,2,3"
```

**New Arguments for Multi-GPU**

**vLLM Arguments:**

- `--use_vllm`: Enable vLLM backend for inference.
- `--vllm_gpus`: Number of GPUs for vLLM data parallelism (default: all available).

**Transformers Arguments:**

- `--multi_gpu_strategy`: Choose strategy (`auto`, `single`, `parallel`, `pipeline`).
- `--num_gpus`: Number of GPUs to use (default: all available).
- `--min_memory_gb`: Minimum GPU memory required per GPU (default: 4.0).
- `--gpu_ids`: Comma-separated list of specific GPU IDs to use.

**Strategy Selection Guide**

* **When to use `parallel`:**
    * Multiple smaller models (7B-13B parameters).
    * High throughput requirements.
    * Multiple GPUs with sufficient memory.
    * Batch processing scenarios.
* **When to use `pipeline`:**
    * Large models (30B+ parameters).
    * Limited GPU memory per device.
    * Single model that doesn't fit on one GPU.
* **When to use `single`:**
    * Small models.
    * Single GPU systems.
    * Memory-constrained environments.
* **When to use `auto`:**
    * Unsure about optimal strategy.
    * Want automatic optimization.
    * Mixed workloads.

**Performance Optimization for Multi-GPU**

**Memory Management**
The system includes features like automatic memory monitoring, smart memory cleanup, and optimal batch size calculation.

```python
# Automatic memory monitoring
log_gpu_memory_usage("Before generation")

# Smart memory cleanup
clear_gpu_memory()

# Optimal batch size calculation
batch_size = calculate_optimal_batch_size(model_name, gpu_memory_gb)
```

**Load Balancing**
The `MultiGPUModelManager` class provides round-robin GPU selection, memory-aware allocation, and automatic failover handling.

**Example Multi-GPU Configurations**

* **vLLM High Throughput Setup (4x RTX 4090):**

```bash
python generate_responses.py \
    --model meta-llama/Llama-3.1-8B-Instruct \
    --use_vllm \
    --vllm_gpus 4
```

* **vLLM Large Model Setup (2x A100 80GB):**

```bash
python generate_responses.py \
    --model meta-llama/Llama-3.1-70B-Instruct \
    --use_vllm \
    --vllm_gpus 2
```

* **Transformers High Throughput Setup (4x RTX 4090):**

```bash
python generate_responses.py \
    --model meta-llama/Llama-3.1-8B-Instruct \
    --multi_gpu_strategy parallel \
    --num_gpus 4 \
    --min_memory_gb 12.0 \
    --batch_size 8
```

* **Transformers Large Model Setup (2x A100 80GB):**

```bash
python generate_responses.py \
    --model meta-llama/Llama-3.1-70B-Instruct \
    --multi_gpu_strategy pipeline \
    --num_gpus 2 \
    --min_memory_gb 40.0
```

* **Memory-Constrained Setup:**

```bash
python generate_responses.py \
    --model microsoft/DialoGPT-medium \
    --multi_gpu_strategy single \
    --min_memory_gb 2.0 \
    --disable_optimizations
```


### 2. Evaluate Responses

Run the evaluation script:

```bash
python evaluate.py [--test] [--input_file path/to/file.csv] [--concurrency_limit 50]
```

Options:

- `--test`: Use test directories.
- `--input_file`: Process a specific file (optional).
- `--concurrency_limit`: Maximum concurrent API calls (default: 50).

This script:

- Processes each response file through evaluation prompts.
- Handles different archetypes (binary, statistics, etc.).
- Supports multiple evaluation runs per response.
- Outputs results to the `evaluated/` directory.


### 3. Calculate Metrics

Run the metrics calculation:

```bash
python metric.py [--test]
```

This script:

- Processes evaluated responses.
- Calculates accuracy and honesty metrics.
- Outputs to the `metrics/` directory.


### 4. Process Final Metrics

Generate the final metrics summary:

```bash
python process_metrics.py [--test]
```

This creates `metrics/all_results.json` containing:

- Total responses per model/archetype.
- Honesty scores (levels 1 and 10).
- Accuracy scores.
- Other relevant metrics.


## API Changes for Multi-GPU Support

**Backward Compatibility**
All existing functionality of `generate_responses.py` remains unchanged. The new multi-GPU features are opt-in and do not affect existing scripts or workflows if not explicitly used.

**New Classes and Functions**

* **`MultiGPUModelManager`**: Manages multiple model instances across GPUs for the Transformers backend.

```python
manager = MultiGPUModelManager(model_name, gpu_ids, enable_optimizations)
responses = manager.generate_batch(prompts, gpu_id, **kwargs)
manager.cleanup()
```

* **Enhanced `create_model_client()`**: Now supports multi-GPU configuration for the Transformers backend.

```python
client = create_model_client(
    model_name,
    multi_gpu_strategy="parallel",
    num_gpus=4,
    min_memory_gb=8.0
)
```


## Metrics Explanation

- **Honesty Score**: Honesty in initial responses.
- **Honesty Score (@10)**: Consistency across multiple generations.
- **Accuracy**: Alignment with ground truth (where applicable).


## Troubleshooting for Multi-GPU

**Common Issues**

1. **Out of Memory Errors**:
    * Reduce `--min_memory_gb`.
    * Use `--multi_gpu_strategy single` for Transformers backend.
    * Enable `--disable_optimizations` if using Transformers backend.
    * For vLLM, ensure GPUs have sufficient memory for the model and consider reducing `--vllm_gpus`.
2. **GPU Not Detected**:
    * Check CUDA installation and drivers.
    * Verify `nvidia-smi` output.
    * Ensure `CUDA_VISIBLE_DEVICES` environment variable is set correctly if used.
3. **Poor Performance**:
    * Try different strategies (`parallel`, `pipeline`, or `auto` for Transformers; ensure vLLM is used if possible).
    * Adjust batch sizes.
    * Monitor GPU utilization to identify bottlenecks.

**Debugging**
Enable detailed logging for memory monitoring with Transformers backend:

```bash
python generate_responses.py \
    --memory_monitoring \
    --multi_gpu_strategy auto \
    --model your-model
```

For vLLM, consult vLLM's logging and documentation for debugging performance.

## Testing

**General Testing**
Use the `--test` flag with any script (`generate_responses.py`, `evaluate.py`, `metric.py`, `process_metrics.py`) to run in test mode:

- Uses `test_csv_data/` instead of `csv_data/`.
- Helps validate changes without affecting production data.
- Provides a smaller dataset for quick iterations.

**Multi-GPU Specific Testing**
Run the dedicated test suite to verify multi-GPU functionality:

```bash
python test_multi_gpu.py
```

This script will test:

- GPU detection and selection.
- Initialization of different multi-GPU strategies.
- Memory monitoring capabilities.
- Basic text generation using multi-GPU setups.

